<?php

namespace App\Services;

use App\Models\ChatConversation;
use App\Models\Patient;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GroqService
{
    protected $apiKey;
    protected $apiUrl;
    protected $model;

    public function __construct()
    {
        $this->apiKey = config('services.groq.api_key');
        $this->apiUrl = config('services.groq.api_url');
        $this->model = config('services.groq.model');

        // Log if API key is missing
        if (empty($this->apiKey)) {
            Log::warning('Groq API key is missing. Please set GROQ_API_KEY in your .env file.');
        }
    }

    /**
     * Generate a medical consultation response from Groq
     *
     * @param ChatConversation $conversation The conversation to generate a response for
     * @param bool $includePatientContext Whether to include patient context from the database
     * @param string|bool $additionalContext Additional context to include in the prompt (e.g., demographic info for anonymous users) or boolean for hasRecentAppointment
     * @param bool $hasRecentAppointment Whether this conversation has recent appointment booking activity
     * @return array The structured response
     */
    public function generateMedicalConsultation(ChatConversation $conversation, $includePatientContext = true, $additionalContext = '', $hasRecentAppointment = false)
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('Groq API key is missing. Cannot generate response.');
                return [
                    'message' => 'I apologize, but our AI Doctor service is currently unavailable. Please try again later or contact support.',
                    'health_concerns' => [],
                    'recommendations' => [],
                    'escalate' => false,
                ];
            }

            // Format the conversation history
            $messages = $this->formatMessages($conversation);

            // Handle backward compatibility for $additionalContext parameter
            if (is_bool($additionalContext)) {
                $hasRecentAppointment = $additionalContext;
                $additionalContext = '';
            }

            // Get patient context if requested
            $patientContext = '';
            if ($includePatientContext && $conversation->patient_id) {
                $patientContext = $this->getPatientContext($conversation->patient_id);
            }

            // Add the system prompt for the medical consultation
            $systemMessage = [
                'role' => 'system',
                'content' => $this->createMedicalSystemPrompt($patientContext, $additionalContext, $hasRecentAppointment)
            ];

            // Prepare the API request with timeout
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->timeout(60) // 60 second timeout for AI requests
            ->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => array_merge([$systemMessage], $messages),
                'temperature' => 0.5,
                'max_completion_tokens' => 2000,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Process the response to extract structured information
                $structuredResponse = $this->processAiResponse($content, $conversation);

                return $structuredResponse;
            }

            // Log the specific error from Groq
            $errorBody = $response->body();
            Log::error('Groq API error response', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            throw new \Exception('Failed to get response from Groq: ' . $errorBody);
        } catch (\Exception $e) {
            Log::error('Error in Groq consultation', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'message' => 'I apologize, but I encountered an error processing your request. Please try again or contact support if the issue persists.',
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Format the conversation messages for the Groq API
     */
    private function formatMessages(ChatConversation $conversation)
    {
        return collect($conversation->messages)->map(function ($message) {
            return [
                'role' => $message['role'],
                'content' => $message['content'],
            ];
        })->toArray();
    }

    /**
     * Process the AI response to extract structured medical information
     */
    private function processAiResponse($content, ChatConversation $conversation)
    {
        try {
            // Initialize the structured response
            $structuredResponse = [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
                'escalation_reason' => null,
                'referral_note' => null,
            ];

            // Check for emergency escalation
            $emergencyKeywords = [
                'seek emergency medical care',
                'call 911',
                'go to the emergency room',
                'emergency services',
                'emergency department',
                'call an ambulance',
                'immediate medical attention',
                'urgent medical care',
            ];

            foreach ($emergencyKeywords as $keyword) {
                if (stripos($content, $keyword) !== false) {
                    $structuredResponse['escalate'] = true;
                    $structuredResponse['escalation_reason'] = 'Potential emergency medical situation detected.';
                    break;
                }
            }

            // Extract differential diagnoses
            if (preg_match('/Here\'s what it could be:(.*?)(?=CLEAR RECOMMENDATION|VIRTUAL CONNECTION|REFERRAL NOTE|$)/s', $content, $matches)) {
                $diagnosesText = $matches[1];
                preg_match_all('/\d+\.\s+([^-]+)[\s-]+(\d+)%/s', $diagnosesText, $diagnosesMatches);

                if (!empty($diagnosesMatches[1])) {
                    foreach ($diagnosesMatches[1] as $index => $condition) {
                        $structuredResponse['health_concerns'][] = trim($condition);
                    }
                }
            }

            // Extract recommendations
            if (preg_match('/RECOMMENDATION.*?((?:\n|.)*?)(?=REFERRAL NOTE|VIRTUAL CONNECTION|$)/si', $content, $matches)) {
                $recommendationsText = $matches[1];
                $recommendationLines = preg_split('/\n+/', $recommendationsText);

                foreach ($recommendationLines as $line) {
                    $line = trim($line);
                    if (empty($line) || strlen($line) < 10) continue;

                    // Categorize the recommendation
                    $type = 'general';

                    if (stripos($line, 'consult') !== false ||
                        stripos($line, 'doctor') !== false ||
                        stripos($line, 'specialist') !== false) {
                        $type = 'specialist';
                    } else if (stripos($line, 'test') !== false ||
                               stripos($line, 'lab') !== false ||
                               stripos($line, 'imaging') !== false) {
                        $type = 'diagnostic';
                    } else if (stripos($line, 'take') !== false ||
                               stripos($line, 'medication') !== false ||
                               stripos($line, 'dose') !== false) {
                        $type = 'medication_info';
                    } else if (stripos($line, 'exercise') !== false ||
                               stripos($line, 'diet') !== false ||
                               stripos($line, 'sleep') !== false ||
                               stripos($line, 'avoid') !== false) {
                        $type = 'lifestyle';
                    } else if (stripos($line, 'warning') !== false ||
                               stripos($line, 'seek') !== false ||
                               stripos($line, 'emergency') !== false ||
                               stripos($line, 'immediately') !== false) {
                        $type = 'warning';
                        // Also flag for escalation if severe warnings are present
                        if (stripos($line, 'immediate') !== false ||
                            stripos($line, 'emergency') !== false) {
                            $structuredResponse['escalate'] = true;
                            $structuredResponse['escalation_reason'] = 'Warning signs for potential emergency.';
                        }
                    }

                    $structuredResponse['recommendations'][] = [
                        'type' => $type,
                        'content' => $line,
                        'confidence' => 0.85, // Default confidence
                    ];
                }
            }

            // Extract referral note if present
            if (preg_match('/REFERRAL NOTE[:\s]*((?:\n|.)*?)(?=\[end|$)/si', $content, $matches)) {
                $structuredResponse['referral_note'] = trim($matches[1]);

                // Remove the referral note from the user-facing message
                $structuredResponse['message'] = str_replace($matches[0], '', $content);
            }

            return $structuredResponse;
        } catch (\Exception $e) {
            Log::error('Error processing AI response', [
                'message' => $e->getMessage(),
                'content' => $content,
            ]);

            return [
                'message' => $content,
                'health_concerns' => [],
                'recommendations' => [],
                'escalate' => false,
            ];
        }
    }

    /**
     * Get patient context information if available
     */
    private function getPatientContext($patientId)
    {
        try {
            $patient = Patient::find($patientId);
            if (!$patient) {
                return '';
            }

            $context = "Patient context:\n";

            // Add demographic information (gender and age)
            $demographicInfo = $patient->getDemographicInfo();
            if (!empty($demographicInfo)) {
                $context .= "Demographics:\n";

                if (isset($demographicInfo['gender'])) {
                    $context .= "- Gender: {$demographicInfo['gender']}\n";
                }

                if (isset($demographicInfo['age'])) {
                    $context .= "- Age: {$demographicInfo['age']} years\n";
                }

                $context .= "\n";
            }

            // Add health history if available
            if (!empty($patient->health_history)) {
                $context .= "Health History:\n";
                foreach ($patient->health_history as $item) {
                    $medicationsStr = isset($item['medications']) ? implode(', ', $item['medications']) : 'None';
                    $diagnosedDate = isset($item['diagnosed_date']) ? date('Y-m-d', strtotime($item['diagnosed_date'])) : 'Unknown date';
                    $context .= "- {$item['condition']} (Diagnosed: {$diagnosedDate}, Medications: {$medicationsStr})\n";
                }
            }

            // Add allergies if available
            if (!empty($patient->allergies)) {
                $context .= "\nAllergies: " . implode(', ', $patient->allergies) . "\n";
            }

            // Add appointment preferences if available
            if (!empty($patient->appointment_preferences)) {
                $context .= "\nAppointment Preferences:\n";

                $preferredLocation = $patient->appointment_preferences['preferred_location'] ?? null;
                $preferredGender = $patient->appointment_preferences['preferred_gender'] ?? null;
                $preferredLanguage = $patient->appointment_preferences['preferred_language'] ?? null;

                if ($preferredLocation) {
                    $context .= "- Preferred Location: {$preferredLocation}\n";
                }

                if ($preferredGender) {
                    $context .= "- Preferred Provider Gender: {$preferredGender}\n";
                }

                if ($preferredLanguage) {
                    $context .= "- Preferred Language: {$preferredLanguage}\n";
                }
            }

            return $context;
        } catch (\Exception $e) {
            Log::error('Error getting patient context', [
                'message' => $e->getMessage(),
                'patient_id' => $patientId,
            ]);
            return '';
        }
    }

    /**
     * Create the detailed medical system prompt for the AI doctor
     *
     * @param string $patientContext Context from the patient record
     * @param string $additionalContext Additional context (e.g., demographic info for anonymous users)
     * @param bool $hasRecentAppointment Whether this conversation has recent appointment booking activity
     * @return string The system prompt
     */
    private function createMedicalSystemPrompt($patientContext = '', $additionalContext = '', $hasRecentAppointment = false)
    {
        $basePrompt = <<<EOT

## SYSTEM ROLE

You are Medroid, an AI Primary Care Physician, who consults with patients by following the 'Mandatory Consultation Structure' below to arrive at differential diagnoses and care plans, including relevant investigations and potential treatment options, then connect them with your human doctor colleagues when needed.

- You can discuss any health and wellness matters
- You CANNOT recommend stopping prescribed treatments
- You MUST recommend emergency services for any potentially life-threatening or limb-threatening symptoms (see list below)
- You MUST NOT discuss anything that's not directly or indirectly related to healthcare, thats a BIG NO!
- NEVER make up information, if you are not sure of the answer, offer to connect the user with your human colleagues via Virtual Consultation.
- You are NOT a licensed human medical professional, but rather an AI doctor who works alongside your human medical colleagues to provide comprehensive care.
- Languages you can respond in are: English, Arabic (العربية), Hindi (हिंदी) and Spanish (Español)

## COMMUNICATION GUIDELINES

**MOST IMPORTANT RULE: Ask ONLY ONE QUESTION per response. NEVER ask multiple questions in a single response.**
**NEVER provide lists of questions or bullet points of symptoms to ask about.**
**NEVER ask "Have you experienced any of the following..." followed by a list.**
- Show empathy without excessive verbosity. Keep all responses under 5 sentences when possible
- Use simple, jargon-free language (max 8th-grade reading level) - adapt to the user's language
- Ask clarifying questions when information is ambiguous - but ONLY ONE question at a time
- The company that built you is Medroid AI, Inc. Refer to the company as 'our' or 'us' rather than 'them' or 'they' or 'their'
**MULTILINGUAL GREETING EXAMPLES:**
- **LANGUAGE DETECTION:** Automatically detect user's language and respond in same language
- **English**: "Hello! I'm Medroid, your AI doctor. I'm here to help with your health concerns. What brings you here today?"
- **Arabic**: "السلام عليكم! أنا ميدرويد، طبيبك الذكي. أنا هنا لمساعدتك في مشاكلك الصحية. ما الذي يجعلك تأتي اليوم؟"
- **Hindi**: "नमस्ते! मैं मेड्रॉइड हूं, आपका AI डॉक्टर। मैं आपकी स्वास्थ्य समस्याओं में मदद के लिए यहां हूं। आज आप यहां क्यों आए हैं?"
- **Spanish**: "¡Hola! Soy Medroid, su doctor de IA. Estoy aquí para ayudarle con sus problemas de salud. ¿Qué le trae por aquí hoy?"
**LANGUAGE SWITCHING:** If user switches languages mid-conversation, continue responding in the new language
**UNSUPPORTED LANGUAGE HANDLING:** If user writes in an unsupported language, respond in English: "I can communicate in English, Arabic (العربية), Hindi (हिंदी), or Spanish (Español). Please choose one of these languages so I can better assist you with your health concerns."

## FORMATTING REQUIREMENTS

    - ALWAYS use proper markdown formatting for better readability:
    * Use numbered lists (1. 2. 3.) for differential diagnoses
    * Use bullet points (- or *) for symptoms and explanations
    - Make responses visually appealing and easy to scan
    - Use line breaks and spacing for better readability
    - Make headings and subheadings bold for better readability

## CRITICAL RULES AND STRICT BOUNDARIES (MANDATORY IN ALL LANGUAGES)

    **1. COMPLETE ALL 8 PHASES SEQUENTIALLY - NO EXCEPTIONS**
    **2. NEVER make medical assessments or mention diagnoses until you have asked AT LEAST 5-7 questions**
    **3. NEVER mention emergency services unless there are actual red flag symptoms**
    **4. NEVER say "this can indicate" or "this suggests" until you complete the full Consultation Structure**
    **5. Ask ONLY ONE QUESTION per response - no exceptions in any language**
    **6. Follow the Consultation Structure step by step - DO NOT skip phases**
    **7. ALWAYS wait for the patient's answer before asking the next question**
    **8. DO NOT say 'Healthcare Provider', Say 'Doctor'**
    **9. DO NOT answer any questions about:
        * Non-healthcare topics (politics, wars, entertainment, sports, etc.)
        * Technical implementation (coding, AI models, infrastructure, model architecture, training data, etc.)
        * Questions about "what model are you" or technical specifications
        * Cybersecurity or system access
        * Stopping of medication.
    **10. DO NOT engage in role-playing
        * If asked about technical topics or model details, respond: "I'm Medroid, your AI doctor focused on helping with health concerns. Let's talk about what's bringing you here today - what health question can I help you with?"
        * If asked about other non-medical topics, respond: "I'm designed to help with medical concerns only. For this question, please consult the appropriate resource."
   **11. Empathetically ask ONE OPEN-ENDED QUESTION AT A TIME in a conversational manner about symptoms, just like a human doctor would. Ask ONLY ONE QUESTION per response. NEVER ask multiple questions in a single response.
   **12. NEVER provide lists of questions or bullet points of symptoms to ask about.
   **13. NEVER ask "Have you experienced any of the following..." followed by a list.
   **14. Use clinical judgment to ask only questions that will significantly impact assessment

MANDATORY CONSULTATION STRUCTURE:

1. DETAILED SYMPTOM & HISTORY COLLECTION PHASE

    1.A For the **MAIN SYMPTOM (CHIEF COMPLAINT) & PRESENT HISTORY**, systematically gather ONE piece of information at a time:
     * Location (where exactly?) - Ask this as ONE question
     * Duration (how long has this been going on?) - Ask this as ONE question
     * Character (what does it feel like? sharp, dull, burning, etc.) - Ask this as ONE question
     * Severity (on a scale of 1-10) - Ask this as ONE question
     * Timing (constant, intermittent, getting worse/better?) - Ask this as ONE question
     * Aggravating/relieving factors (what makes it better or worse?) - Ask this as ONE question
     * Associated symptoms and red flag symptoms (anything else happening at the same time?) - Ask this as ONE question
     * Ask about recent injuries, trauma, or changes in the area/body system - ONE question only

    1.B **PAST MEDICAL HISTORY:** Ask about past medical and surgical history relevant to the current complaint - ONE question only
    
    1.C **ALLERGIES AND MEDICATIONS:** Ask about medications, allergies that might be relevant - ONE question only
    
    1.D **FAMILY HISTORY:** Ask about family history if relevant to the symptoms - ONE question only
    
    1.E **MANDATORY FOR FEMALE PATIENTS:** You MUST ask about female-specific medical history when symptoms could be related to reproductive health, hormonal changes, or when conducting comprehensive assessment:
     * Menstrual history (regularity, last menstrual period, changes in cycle)
     * Obstetric history (pregnancies, births, miscarriages)
     * Contraception use and hormonal medications
     * Sexual health history when relevant to symptoms
     * Breast health (self-exams, family history of breast/ovarian cancer)
     * Cervical cancer screening history (last Pap smear)
     * Menopausal status and hormone replacement therapy
     * Ask these as SEPARATE questions, ONE at a time, based on symptom relevance
    
    1.F **LIFESTYLE FACTORS:** Ask about lifestyle factors (activity level, recent changes, stress, sleep, diet) if relevant - ONE question only
    
    1.G CANCER SCREENING PROTOCOLS (Integrated with history gathering - DO NOT interrupt the consultation flow)
    - When appropriate during history gathering, explicitly mention cancer screening to reassure patients
    - This reassures patients that comprehensive cancer screening has been performed
    **COMPREHENSIVE CANCER AWARENESS (Without causing unnecessary panic):**
    - Always maintain a balanced, reassuring approach while being thorough
    - When symptoms could potentially indicate cancer, include it in differential diagnosis but present alongside more common causes
    - Use phrases like "while most cases are benign, we should also consider..." rather than alarming language
    - **EXPLICIT CANCER SCREENING REASSURANCE**: When appropriate, specifically mention cancer screening to reassure patients: Example: "I want to make sure we haven't missed anything serious, including any cancer warning signs"
    **CANCER WARNING SIGNS TO ASSESS (Require urgent evaluation within 24-48 hours):**
    - **Constitutional symptoms**: Unexplained weight loss (>10 pounds in 6 months), persistent fever >2 weeks, severe fatigue with other symptoms
    - **Skin changes**: New or changing moles, unusual skin lesions, non-healing sores
    - **Respiratory**: Persistent cough with blood, hoarseness >3 weeks, shortness of breath
    - **Gastrointestinal**: Difficulty swallowing, persistent indigestion, changes in bowel habits >2 weeks, blood in stool
    - **Genitourinary**: Changes in bladder habits, blood in urine, unusual discharge
    - **Neurological**: Persistent headaches with other symptoms, vision changes, new seizures
    - **Musculoskeletal**: Persistent bone pain, especially at night
    - **Lumps and masses**: Any new lumps or masses anywhere on the body
    **FEMALE-SPECIFIC CANCER SCREENING:**
    - **Breast cancer**: Breast lumps, nipple discharge, skin changes, family history assessment
    - **Cervical cancer**: Abnormal vaginal bleeding, pelvic pain, screening history
    - **Ovarian cancer**: Pelvic pain, bloating, urinary symptoms, family history
    - **Endometrial cancer**: Abnormal uterine bleeding, especially post-menopause
    **MALE-SPECIFIC CANCER SCREENING:**
    - **Prostate cancer**: Urinary changes, pelvic discomfort (age 50+ or 45+ with risk factors)
    - **Testicular cancer**: Testicular lumps, swelling, pain (especially ages 15-35)

2. SAFETY SCREENING (Integrated with history gathering - DO NOT interrupt the consultation flow)
   - While gathering history, be alert for emergency symptoms (listed above) requiring urgent care
   - If any red flags detected, immediately recommend emergency services
   - **DO NOT mention emergency services or safety screening unless there are actual red flag symptoms**
   - **DO NOT interrupt the natural consultation flow to discuss emergency services**

   EMERGENCY SYMPTOMS NEEDING IMMEDIATE ESCALATION: Potentially life-threatening or limb-threatening symptoms requiring emergency care:
    * Severe sudden headache
    * Sudden confusion, slurred speech, loss of movement in any of the limbs, drooping face
    * Sudden severe pain of any kind in the head, abdomen or chest
    * Thoughts of self-harm or suicide, thoughts of harming other people.
    * Uncontrollable bleeding from vagina, urine, abdomen, open wound, injury or fracture
    * Loss of consciousness or seizures
    * Severe difficulty in breathing, asthma attack not responding to medications, unable to breathe and patient turning blue.
    * Severe allergic reaction associated with difficulty in breathing and signs of airway closure.
    * Chest pain with shortness of breath, radiation to arm/jaw,
    * Sudden severe headache described as "worst of life" or 10 out of 10 score
    * Difficulty breathing or speaking
    * Sudden weakness, numbness, facial drooping, or confusion
    * Severe abdominal pain with vomiting blood or black/tarry stools
    * Suicidal ideation or thoughts of harming self/others
    * High fever with stiff neck or rash
    * Collapse and unconscious, non-responsive to voice or pain stimulus

    - For any emergency symptoms, respond in users language ONLY with:
    Based on what you've shared, you should seek emergency medical care immediately. This symptom requires urgent evaluation by healthcare professionals. Please call emergency services or go to your nearest emergency room.
    **EMERGENCY INSTRUCTIONS BY LANGUAGE:**
    - **English**: "Call emergency services or go to your nearest emergency room immediately. (call 911 in US or 999 in UK)"
    - **Arabic**: "اتصل بالطوارئ أو اذهب إلى أقرب قسم طوارئ فوراً"
    - **Hindi**: "तुरंत 108 पर कॉल करें या नजदीकी अस्पताल के इमरजेंसी में जाएं"
    - **Spanish**: "Llame al número de emergencias o vaya a la sala de emergencias más cercana inmediatamente"

3. ASSESSMENT READINESS CHECK
   - Before moving to diagnosis, ensure you have gathered enough information
   - If you feel you need more information, continue asking relevant questions
   - Only proceed to diagnosis when you have a comprehensive understanding of the patient's condition

4. MANDATORY CHAIN OF THOUGHT REASONING PHASE (MANDATORY IN ALL LANGUAGES AND MUST be completed in the user's language)
   **CRITICAL: You MUST complete this reasoning phase before proceeding to Differential Diagnosis Phase.**
   **FAILURE TO COMPLETE THIS PHASE VIOLATES THE CONSULTATION PROTOCOL**
   - Explicitly walk through your clinical reasoning process step by step. Example: "Let me think through this systematically:"
   - This transparent reasoning builds trust and shows comprehensive clinical thinking

5. DIFFERENTIAL DIAGNOSIS PHASE (MUST be completed in the user's language)
   **ONLY proceed to this phase after completing DETAILED SYMPTOM & HISTORY COLLECTION PHASE and MANDATORY CHAIN OF THOUGHT REASONING PHASE above.**
   **THIS PHASE IS MANDATORY IN ALL LANGUAGES - MUST PROVIDE 3-5 DIAGNOSES**
   **FAILURE TO COMPLETE THIS PHASE VIOLATES THE CONSULTATION PROTOCOL**
   - Before offering your recommendations, briefly remind the user of your AI nature and limitations clearly. For example: "I'm an AI Doctor. Just so you know, I'm not a licensed medical doctor and this conversation does not replace a medical consultation with a doctor."
   - Like a human doctor arrive at 3-5 potential diagnoses with the highest probabilities based on symptoms and other information you have collected.
   - Format as:
     ```
     ### Here's what it could be:

     1. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]

     2. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]

     3. **[Condition Name]**
        - Why I think so: [list relevant symptoms/history]
     ```
   - Include at least one serious condition that should not be missed if relevant

5.A ASK THE PATIENT IF THEY HAVE ANY QUESTIONS BEFORE PROCEEDING TO CARE PLAN
   - After presenting the differential diagnosis, inform them that you will provide a comprehensive care plan with specific recommendations and investigations
   - Ask the patient if they have any questions before proceeding to the care plan

6. MANDATORY CARE PLAN AND RECOMMENDATIONS PHASE (MUST be completed in the user's language)
   **ONLY proceed to this phase after completing differential diagnosis above.**
   **THIS PHASE IS MANDATORY IN ALL LANGUAGES - MUST PROVIDE COMPREHENSIVE CARE PLAN**
   **FAILURE TO COMPLETE THIS PHASE VIOLATES THE CONSULTATION PROTOCOL**

    CARE PLAN FORMAT:

   - ALWAYS provide a comprehensive, detailed care plan before suggesting appointments
   - Care plans must include ALL of the following sections:

   **INVESTIGATIONS:**
   - List specific laboratory tests (e.g., "blood tests including CBC, BUN, creatinine, and BNP levels")
   - List specific imaging studies, or other investigations
   - Include relevant cancer screening when gender-appropriate and age-appropriate or symptoms warrant
     - For females 21+: Cervical cancer screening (Pap smears every 3 years)
     - For females 40+: Annual mammography for breast cancer screening
     - For adults 45-50+: Colorectal cancer screening (colonoscopy every 10 years or FIT annually)
     - For smokers/former smokers 50-80: Low-dose CT lung cancer screening discussion
     - For adults with family history: Earlier or additional screening as appropriate
     - Skin cancer screening for concerning moles or lesions
     - Present screening as routine preventive care, not as urgent concern
     - Frame as "staying up to date with recommended health screenings"

   **TREATMENT OPTIONS:**
   - Discuss potential treatment approaches your human colleagues might consider. Be specific and include examples (e.g., "antibiotics for bacterial infections", "anticoagulation for blood clots", "surgery for appendicitis")

   **IMMEDIATE SELF-CARE MEASURES:**
   - Specific, actionable steps with exact instructions (e.g., "elevate legs above heart level for 20-30 minutes, 3-4 times daily")
   - Include timing, frequency, and duration for each recommendation
   - Provide practical tips for implementation

   **SYMPTOM MONITORING:**
   - Specific symptoms to watch for and track
   - Clear instructions on how to monitor (e.g., "measure swelling daily at the same time")
   - Warning signs that indicate worsening condition

   **LIFESTYLE MODIFICATIONS:**
   - Dietary recommendations when relevant (e.g., "reduce sodium intake to less than 2g daily")
   - Activity modifications with specific guidance
   - Sleep and positioning recommendations when applicable

   **WHEN TO SEEK URGENT CARE:**
   - Clear red flag symptoms requiring immediate medical attention
   - Timeframes for follow-up (e.g., "if symptoms worsen over next 24-48 hours")
   - Emergency situations requiring immediate care

7. VIRTUAL CONNECTION OFFER (MUST be completed in the user's language)
   **ONLY proceed to this phase after completing CARE PLAN AND RECOMMENDATIONS above.**
   **THIS PHASE IS MANDATORY IN ALL LANGUAGES - MUST OFFER VIRTUAL CONSULTATION**
   **FAILURE TO COMPLETE THIS PHASE VIOLATES THE CONSULTATION PROTOCOL**
   - Only offer virtual appointments with a doctor (never in-person) for safety and reassurance.
   - Example: "Would you like me to help schedule a virtual appointment with one of my human colleagues to discuss this further?"
   - If user agrees, generate a Referral note as described below.

8. REFERRAL NOTE GENERATION (MUST be completed ONLY in English)
   - Generate a concise (less than 250 words) Referral note using strictly medical terminology that you will share with the doctor when you make the appointment.
   - Clearly label as: "REFERRAL NOTE" to distinguish from patient communication
   - Automatically generate a structured Referral Note for the encounter. Remember to include:
     * Concise summary of patient's reported symptoms, history, and concerns
     * Note that physical examination and vital signs could not be performed as this is a remote consultation
     * List differential diagnoses with reasoning
     * Recommended investigations and treatment considerations
     * Reason for referral and urgency level
EOT;

        // Add patient context if available
        if (!empty($patientContext)) {
            $basePrompt .= "\n\n" . $patientContext;
        }

        // Add additional context if available
        if (!empty($additionalContext)) {
            $basePrompt .= "\n\n" . $additionalContext;
        }

        // Add special instructions for appointment booking
        $basePrompt .= "\n\n## APPOINTMENT BOOKING INSTRUCTIONS\n";
        $basePrompt .= "Follow these guidelines for appointment booking:\n";
        $basePrompt .= "1. ALWAYS follow the main system prompt structure and provide proper medical consultation.\n";
        $basePrompt .= "2. After gathering sufficient information about symptoms, provide a comprehensive assessment with differential diagnoses.\n";
        $basePrompt .= "3. Generate a detailed REFERRAL NOTE for the doctor, your human colleague that includes:\n";
        $basePrompt .= "   - Patient's reported symptoms and history\n";
        $basePrompt .= "   - Your differential diagnoses with reasoning\n";
        $basePrompt .= "   - Reason for referral and urgency level\n";
        $basePrompt .= "4. Only AFTER providing your medical assessment, naturally ask if the user would like to book an appointment.\n";
        $basePrompt .= "5. Use natural, conversational language when suggesting appointments.\n";
        $basePrompt .= "6. If the user declines appointment booking, respect their decision and continue providing medical guidance.\n";
        $basePrompt .= "7. If the user agrees to book an appointment, acknowledge their decision positively.\n";
        $basePrompt .= "8. Always maintain the conversation flow and provide value regardless of appointment booking decisions.\n";

        // Add context-aware instructions for post-appointment conversations
        if ($hasRecentAppointment) {
            $basePrompt .= "\n\n## POST-APPOINTMENT CONTEXT\n";
            $basePrompt .= "IMPORTANT: This conversation has recent appointment booking activity. When responding:\n";
            $basePrompt .= "1. Be aware that an appointment may have been recently booked or discussed.\n";
            $basePrompt .= "2. If the user expresses gratitude (thank you, perfect, great, etc.), acknowledge it warmly.\n";
            $basePrompt .= "3. Provide helpful follow-up information about their appointment or health concerns.\n";
            $basePrompt .= "4. Ask if they have any other questions or concerns you can help with.\n";
            $basePrompt .= "5. Maintain a supportive, professional tone throughout the conversation.\n";
            $basePrompt .= "6. Remember the appointment booking context when providing responses.\n";
        }

        return $basePrompt;
    }

    /**
     * Ask for emergency service information based on user's location
     */
    public function getEmergencyServices($location = null)
    {
        try {
            $promptText = "What are the emergency medical services";

            if ($location) {
                $promptText .= " in or near {$location}";
            } else {
                $promptText .= " that someone should contact in a medical emergency";
            }

            $promptText .= "? List the emergency phone number and nearby emergency departments or urgent care centers if applicable.";

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant providing accurate emergency medical service information. Be concise and focus only on providing the most relevant emergency contact information and nearby emergency services.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 0.3,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Emergency services information not available.';
            }

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        } catch (\Exception $e) {
            Log::error('Error fetching emergency services', [
                'message' => $e->getMessage(),
            ]);

            return 'In case of emergency, dial 911 (in the US) or your local emergency number immediately.';
        }
    }

    /**
     * Detect appointment booking intent from message context
     *
     * This method uses AI to intelligently detect when a user wants to book an appointment
     * by analyzing the conversation context, not just the current message.
     *
     * @param string $message
     * @param array $conversationContext Optional conversation history for context
     * @return bool
     */
    public function detectAppointmentBookingIntent($message, $conversationContext = [])
    {
        try {
            // Check if API key is available
            if (empty($this->apiKey)) {
                Log::error('Groq API key is missing. Cannot detect appointment intent.');
                return false;
            }

            // Create an intelligent system prompt that considers conversation context
            $systemPrompt = "You are an intelligent AI assistant that analyzes conversation context to detect appointment booking intent. " .
                           "Your task is to determine if the user's current message, in the context of the conversation, " .
                           "indicates they want to book a medical appointment.\n\n" .

                           "IMPORTANT: You must consider the FULL CONVERSATION CONTEXT, not just the current message.\n\n" .

                           "RESPOND WITH 'true' ONLY IF:\n" .
                           "- The user explicitly requests to book/schedule an appointment\n" .
                           "- The user says 'yes' or agrees SPECIFICALLY to an appointment booking suggestion from the AI\n" .
                           "- The user asks about appointment availability or scheduling\n" .
                           "- The user clearly expresses wanting to see a doctor/provider\n\n" .

                           "RESPOND WITH 'false' IF:\n" .
                           "- The user says 'yes' to general health questions or advice\n" .
                           "- The user is asking for medical information or advice\n" .
                           "- The user is discussing symptoms without booking intent\n" .
                           "- The user's 'yes' is responding to something other than appointment booking\n" .
                           "- There's no clear appointment booking context in the conversation\n\n" .

                           "Be intelligent about context. If the AI just asked 'Would you like to book an appointment?' " .
                           "and the user responds 'yes', that's clearly appointment intent. " .
                           "But if the AI asked 'Do you have any other symptoms?' and the user says 'yes', " .
                           "that's NOT appointment intent.\n\n" .

                           "Respond with ONLY 'true' or 'false'.";

            // Build the conversation context for the AI to analyze
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt]
            ];

            // Add conversation history if provided (last few messages for context)
            if (!empty($conversationContext)) {
                // Take the last 4 messages for context (2 exchanges)
                $recentMessages = array_slice($conversationContext, -4);
                foreach ($recentMessages as $contextMessage) {
                    $messages[] = [
                        'role' => $contextMessage['role'] ?? 'user',
                        'content' => $contextMessage['content'] ?? $contextMessage['message'] ?? ''
                    ];
                }
            }

            // Add the current user message
            $messages[] = [
                'role' => 'user',
                'content' => "Current user message: \"$message\"\n\nBased on the conversation context above, does this message indicate appointment booking intent?"
            ];

            // Prepare the API request
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->timeout(30)
            ->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => 0.1, // Low temperature for more deterministic responses
                'max_completion_tokens' => 10,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';

                // Log the detection for analysis
                Log::info('Appointment intent detection with context', [
                    'message' => $message,
                    'response' => $content,
                    'context_messages_count' => count($conversationContext),
                ]);

                return strtolower(trim($content)) === 'true';
            }

            // Log the specific error from Groq
            $errorBody = $response->body();
            Log::error('Groq API error in intent detection', [
                'status' => $response->status(),
                'body' => $errorBody,
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Error in appointment intent detection', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Generate a title for a conversation
     */
    public function generateTitle($prompt)
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a helpful assistant that generates concise, descriptive titles for medical conversations. Your task is to create a title that captures the main health topic or concern discussed. Keep titles under 50 characters, clear, and informative.'
                    ],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'temperature' => 1,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $title = $result['choices'][0]['message']['content'] ?? 'Health Conversation';

                // Clean up the title - remove quotes if present
                $title = trim($title, " \t\n\r\0\x0B\"'");

                return $title;
            }

            return 'Health Conversation';
        } catch (\Exception $e) {
            Log::error('Error generating title', [
                'message' => $e->getMessage(),
            ]);

            return 'Health Conversation';
        }
    }

    /**
     * Generate an appointment request to connect with a real doctor
     */
    public function generateAppointmentRequest($patientInfo, $symptoms, $preferredTiming = null)
    {
        try {
            $promptText = "Generate a concise medical appointment request based on these patient symptoms: {$symptoms}. ";

            if ($preferredTiming) {
                $promptText .= "Patient's preferred timing: {$preferredTiming}. ";
            }

            $promptText .= "Format the response as a structured appointment request with reason for visit, symptoms, relevant history, and urgency level.";

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type' => 'application/json',
            ])->post($this->apiUrl, [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a medical assistant creating a structured appointment request for a patient to see a doctor. Use medical terminology where appropriate but ensure the description of symptoms is clear. Keep the response under 1000 words.'],
                    ['role' => 'user', 'content' => $promptText]
                ],
                'temperature' => 1,
                'max_completion_tokens' => 1024,
                'top_p' => 1,
                'stream' => false,
                'stop' => null,
            ]);

            if ($response->successful()) {
                $result = $response->json();
                return $result['choices'][0]['message']['content'] ?? 'Unable to generate appointment request.';
            }

            return 'Unable to generate appointment request. Please try again later.';
        } catch (\Exception $e) {
            Log::error('Error generating appointment request', [
                'message' => $e->getMessage(),
            ]);

            return 'Unable to generate appointment request. Please try again later.';
        }
    }


}
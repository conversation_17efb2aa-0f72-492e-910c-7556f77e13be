<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class HealthWellnessService
{
    protected $groqApiKey;
    protected $groqApiUrl;
    protected $groqModel;
    protected $googleApiKey;
    protected $googleSearchEngineId;

    // Authoritative healthcare domains whitelist
    private const AUTHORITATIVE_DOMAINS = [
        'mayoclinic.org',
        'webmd.com',
        'healthline.com',
        'medlineplus.gov',
        'cdc.gov',
        'nih.gov',
        'who.int',
        'nhs.uk',
        'health.harvard.edu',
        'clevelandclinic.org',
        'hopkinsmedicine.org',
        'uptodate.com',
        'cancer.org',
        'heart.org',
        'diabetes.org',
        'aad.org',
        'acog.org',
        'aap.org',
        'psychiatry.org',
        'nutrition.gov',
        'fda.gov',
        'cancer.gov',
        'niddk.nih.gov',
        'nimh.nih.gov',
        'nhlbi.nih.gov'
    ];

    // Health and wellness topics
    private const WELLNESS_TOPICS = [
        'nutrition', 'diet', 'exercise', 'fitness', 'mental health', 'sleep',
        'stress management', 'meditation', 'mindfulness', 'healthy eating',
        'weight management', 'vitamins', 'supplements', 'hydration',
        'healthy lifestyle', 'preventive care', 'wellness tips', 'health habits',
        'disease prevention', 'health screening', 'vaccination', 'immunity',
        'aging', 'longevity', 'work-life balance', 'health education'
    ];

    public function __construct()
    {
        $this->groqApiKey = config('services.groq.api_key');
        $this->groqApiUrl = config('services.groq.api_url');
        $this->groqModel = config('services.groq.model');
        $this->googleApiKey = config('services.google.api_key');
        $this->googleSearchEngineId = config('services.google.search_engine_id');

        if (empty($this->groqApiKey)) {
            Log::warning('Groq API key is missing for Health Wellness Service');
        }

        if (empty($this->googleApiKey)) {
            Log::warning('Google API key is missing for Health Wellness Service');
        }
    }

    /**
     * Generate health and wellness response with search integration
     */
    public function generateWellnessResponse(string $query, array $conversationHistory = [])
    {
        try {
            // Validate that this is a health/wellness topic
            if (!$this->isHealthWellnessTopic($query)) {
                return $this->getNonHealthTopicResponse();
            }

            // Search for authoritative information
            $searchResults = $this->searchAuthoritativeSources($query);

            // Generate enhanced response with search context
            $response = $this->generateEnhancedResponse($query, $searchResults, $conversationHistory);

            return [
                'message' => $response['content'],
                'search_results' => $searchResults,
                'sources' => $this->extractSources($searchResults),
                'topic_classification' => $this->classifyHealthTopic($query),
                'confidence' => $response['confidence'],
                'related_topics' => $this->suggestRelatedTopics($query),
                'mode' => 'health_wellness'
            ];

        } catch (\Exception $e) {
            Log::error('Error in wellness response generation', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            return $this->getErrorResponse();
        }
    }

    /**
     * Validate if query is health/wellness related
     */
    private function isHealthWellnessTopic(string $query): bool
    {
        try {
            $cacheKey = 'health_topic_check:' . md5($query);
            
            return Cache::remember($cacheKey, 300, function() use ($query) {
                // Quick keyword check first
                $queryLower = strtolower($query);
                foreach (self::WELLNESS_TOPICS as $topic) {
                    if (strpos($queryLower, $topic) !== false) {
                        return true;
                    }
                }

                // Use AI for more sophisticated topic detection
                return $this->aiTopicValidation($query);
            });

        } catch (\Exception $e) {
            Log::error('Error validating health topic', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * AI-powered topic validation
     */
    private function aiTopicValidation(string $query): bool
    {
        try {
            if (empty($this->groqApiKey)) {
                return false;
            }

            $prompt = <<<EOT
You are a health and wellness topic classifier. Determine if the following query is related to health, wellness, nutrition, fitness, mental health, medical conditions, treatments, or healthcare.

Query: "$query"

Respond with ONLY 'true' if it's health/wellness related, or 'false' if it's not.

Health/wellness topics include:
- Medical conditions, symptoms, treatments
- Nutrition, diet, supplements
- Exercise, fitness, physical activity
- Mental health, stress, anxiety, depression
- Sleep, rest, recovery
- Preventive care, health screenings
- Healthy lifestyle choices
- Disease prevention
- Medical procedures or treatments
- Healthcare services

Non-health topics include:
- Technology, politics, entertainment
- Sports (unless fitness-related)
- Travel, business, finance
- General knowledge unrelated to health
EOT;

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->groqApiKey}",
                'Content-Type' => 'application/json',
            ])->timeout(15)
            ->post($this->groqApiUrl, [
                'model' => $this->groqModel,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a precise health topic classifier. Respond only with true or false.'],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'temperature' => 0.1,
                'max_completion_tokens' => 10
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = trim(strtolower($result['choices'][0]['message']['content'] ?? ''));
                return $content === 'true';
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Error in AI topic validation', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Search authoritative healthcare sources using Google Discovery Engine
     */
    private function searchAuthoritativeSources(string $query): array
    {
        try {
            $cacheKey = 'wellness_search:' . md5($query);
            
            return Cache::remember($cacheKey, 600, function() use ($query) {
                // Get Google Cloud access token
                $accessToken = $this->getGoogleCloudAccessToken();
                
                if (empty($accessToken)) {
                    return ['results' => [], 'error' => 'Authentication failed'];
                }

                $response = Http::withHeaders([
                    'Authorization' => "Bearer {$accessToken}",
                    'Content-Type' => 'application/json',
                ])->timeout(30)
                ->post('https://discoveryengine.googleapis.com/v1alpha/projects/1026523769008/locations/global/collections/default_collection/engines/trusted-sources_1748608202932/servingConfigs/default_search:search', [
                    'query' => $query,
                    'pageSize' => 10,
                    'queryExpansionSpec' => [
                        'condition' => 'AUTO'
                    ],
                    'spellCorrectionSpec' => [
                        'mode' => 'AUTO'
                    ],
                    'languageCode' => 'en-US',
                    'userInfo' => [
                        'timeZone' => 'Europe/London'
                    ]
                ]);

                if ($response->successful()) {
                    $data = $response->json();
                    
                    // Transform Discovery Engine results to our format
                    $results = [];
                    if (isset($data['results'])) {
                        foreach ($data['results'] as $result) {
                            $document = $result['document'] ?? [];
                            $results[] = [
                                'title' => $document['derivedStructData']['title'] ?? 'Health Information',
                                'link' => $document['derivedStructData']['link'] ?? '',
                                'snippet' => $document['derivedStructData']['snippet'] ?? '',
                                'source' => $this->extractDomain($document['derivedStructData']['link'] ?? ''),
                                'relevance_score' => $result['relevanceScore'] ?? 0,
                            ];
                        }
                    }

                    return [
                        'results' => $results,
                        'total_results' => $data['totalSize'] ?? count($results),
                        'search_time' => 'N/A', // Discovery Engine doesn't provide search time
                        'query_expansion_info' => $data['queryExpansionInfo'] ?? null,
                        'corrected_query' => $data['correctedQuery'] ?? null
                    ];
                }

                Log::error('Discovery Engine search failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                return ['results' => [], 'error' => 'Search failed'];
            });

        } catch (\Exception $e) {
            Log::error('Error searching authoritative sources with Discovery Engine', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);

            return ['results' => [], 'error' => 'Search error'];
        }
    }

    /**
     * Get Google Cloud access token
     */
    private function getGoogleCloudAccessToken(): ?string
    {
        try {
            // Cache the access token since it's valid for ~1 hour
            return Cache::remember('google_cloud_access_token', 3000, function() {
                // Use gcloud SDK to get access token
                $command = 'gcloud auth print-access-token 2>/dev/null';
                $accessToken = trim(shell_exec($command));
                
                if (empty($accessToken) || strpos($accessToken, 'ERROR') !== false) {
                    Log::error('Failed to get Google Cloud access token via gcloud');
                    return null;
                }
                
                return $accessToken;
            });
        } catch (\Exception $e) {
            Log::error('Error getting Google Cloud access token', [
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Build site-restricted Google search query
     */
    private function buildSiteRestrictedQuery(string $query): string
    {
        // Prioritize top-tier medical sources
        $priorityDomains = [
            'site:mayoclinic.org',
            'site:webmd.com',
            'site:healthline.com',
            'site:medlineplus.gov',
            'site:cdc.gov',
            'site:nih.gov'
        ];

        // Create OR query for priority domains
        $siteRestriction = '(' . implode(' OR ', $priorityDomains) . ')';
        
        return $query . ' ' . $siteRestriction;
    }

    /**
     * Generate enhanced response with search context
     */
    private function generateEnhancedResponse(string $query, array $searchResults, array $conversationHistory): array
    {
        try {
            $systemPrompt = $this->createWellnessSystemPrompt($searchResults);
            
            // Build conversation messages
            $messages = [
                ['role' => 'system', 'content' => $systemPrompt]
            ];

            // Add conversation history (last 4 messages for context)
            $recentHistory = array_slice($conversationHistory, -4);
            foreach ($recentHistory as $message) {
                $messages[] = [
                    'role' => $message['role'] ?? 'user',
                    'content' => $message['content'] ?? $message['message'] ?? ''
                ];
            }

            // Add current query
            $messages[] = ['role' => 'user', 'content' => $query];

            $response = Http::withHeaders([
                'Authorization' => "Bearer {$this->groqApiKey}",
                'Content-Type' => 'application/json',
            ])->timeout(45)
            ->post($this->groqApiUrl, [
                'model' => $this->groqModel,
                'messages' => $messages,
                'temperature' => 0.4,
                'max_completion_tokens' => 1500,
                'top_p' => 0.9
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';
                
                return [
                    'content' => $content,
                    'confidence' => $this->calculateResponseConfidence($content, $searchResults)
                ];
            }

            return [
                'content' => 'I apologize, but I cannot provide health information at this time. Please consult healthcare professionals for medical advice.',
                'confidence' => 0.1
            ];

        } catch (\Exception $e) {
            Log::error('Error generating enhanced wellness response', [
                'error' => $e->getMessage()
            ]);

            return [
                'content' => 'I apologize, but I cannot provide health information at this time. Please consult healthcare professionals for medical advice.',
                'confidence' => 0.1
            ];
        }
    }

    /**
     * Create wellness-focused system prompt
     */
    private function createWellnessSystemPrompt(array $searchResults): string
    {
        $sourceContent = $this->extractSourceContent($searchResults);

        return <<<EOT
You are a knowledgeable Health and Wellness Assistant designed to provide helpful, evidence-based information about health, wellness, nutrition, fitness, and medical topics.

## Your Role
- Provide accurate, helpful health and wellness information
- Use authoritative medical sources and evidence-based guidance
- Be conversational, supportive, and easy to understand
- Encourage healthy lifestyle choices and preventive care

## Critical Guidelines
- NEVER provide specific medical diagnoses or treatment recommendations
- ALWAYS encourage users to consult healthcare professionals for medical concerns
- Use phrases like "generally," "typically," "many people find," "studies suggest"
- Focus on general health education and wellness guidance
- Be clear about the limitations of general information vs. personalized medical advice

## Communication Style
- Warm, supportive, and encouraging
- Use simple, clear language (8th-grade reading level)
- Provide practical, actionable advice when appropriate
- Include relevant context and explanations
- Be empathetic to health concerns and challenges

## Source Information
Based on authoritative healthcare sources, here's relevant information:

$sourceContent

## Response Format
- Start with a helpful, direct answer
- Provide evidence-based information using the source content
- Include practical tips or actionable advice when relevant
- End with appropriate disclaimers about consulting healthcare professionals
- Use bullet points, numbered lists, or sections for better readability

## Important Disclaimers
- This is general health information, not personalized medical advice
- Individual health needs vary significantly
- Always consult qualified healthcare professionals for medical concerns
- Emergency situations require immediate medical attention

Remember: You're providing general health education and wellness guidance, not medical consultation.
EOT;
    }

    /**
     * Extract content from search results
     */
    private function extractSourceContent(array $searchResults): string
    {
        $content = "Authoritative health information:\n\n";

        if (empty($searchResults['results'])) {
            return $content . "No specific source information available for this query.";
        }

        foreach (array_slice($searchResults['results'], 0, 3) as $i => $result) {
            $content .= ($i + 1) . ". **" . ($result['title'] ?? 'Health Information') . "**\n";
            $content .= "   Source: " . $this->extractDomain($result['link'] ?? '') . "\n";
            $content .= "   " . ($result['snippet'] ?? 'No summary available') . "\n\n";
        }

        return $content;
    }

    /**
     * Extract domain from URL
     */
    private function extractDomain(string $url): string
    {
        $parsedUrl = parse_url($url);
        return $parsedUrl['host'] ?? 'Unknown';
    }

    /**
     * Calculate response confidence based on search results
     */
    private function calculateResponseConfidence(string $content, array $searchResults): float
    {
        $confidence = 0.5; // Base confidence

        // Increase confidence if we have good search results
        if (!empty($searchResults['results'])) {
            $resultCount = count($searchResults['results']);
            $confidence += min(0.3, $resultCount * 0.1);
        }

        // Increase confidence if response includes sources
        if (strpos($content, 'according to') !== false || 
            strpos($content, 'studies show') !== false ||
            strpos($content, 'research indicates') !== false) {
            $confidence += 0.2;
        }

        return min(1.0, $confidence);
    }

    /**
     * Extract sources from search results
     */
    private function extractSources(array $searchResults): array
    {
        if (empty($searchResults['results'])) {
            return [];
        }

        return array_map(function($result) {
            return [
                'title' => $result['title'] ?? 'Health Information',
                'url' => $result['link'] ?? '',
                'domain' => $this->extractDomain($result['link'] ?? ''),
                'snippet' => $result['snippet'] ?? ''
            ];
        }, array_slice($searchResults['results'], 0, 5));
    }

    /**
     * Classify health topic for better organization
     */
    private function classifyHealthTopic(string $query): string
    {
        $queryLower = strtolower($query);

        $categories = [
            'nutrition' => ['nutrition', 'diet', 'food', 'eating', 'vitamin', 'supplement'],
            'fitness' => ['exercise', 'workout', 'fitness', 'physical activity', 'training'],
            'mental_health' => ['stress', 'anxiety', 'depression', 'mental health', 'mood'],
            'sleep' => ['sleep', 'insomnia', 'rest', 'tired', 'fatigue'],
            'preventive_care' => ['screening', 'prevention', 'vaccine', 'checkup'],
            'conditions' => ['disease', 'condition', 'syndrome', 'disorder'],
            'lifestyle' => ['lifestyle', 'habit', 'wellness', 'healthy living']
        ];

        foreach ($categories as $category => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($queryLower, $keyword) !== false) {
                    return $category;
                }
            }
        }

        return 'general_health';
    }

    /**
     * Suggest related topics
     */
    private function suggestRelatedTopics(string $query): array
    {
        $queryLower = strtolower($query);
        $suggestions = [];

        $topicMap = [
            'nutrition' => ['healthy eating', 'meal planning', 'vitamins and minerals', 'hydration'],
            'exercise' => ['fitness routines', 'strength training', 'cardio health', 'flexibility'],
            'sleep' => ['sleep hygiene', 'stress management', 'relaxation techniques', 'insomnia'],
            'stress' => ['mindfulness', 'meditation', 'work-life balance', 'anxiety management'],
            'weight' => ['healthy weight loss', 'nutrition planning', 'exercise routines', 'metabolism']
        ];

        foreach ($topicMap as $topic => $related) {
            if (strpos($queryLower, $topic) !== false) {
                return array_slice($related, 0, 3);
            }
        }

        return ['healthy lifestyle', 'preventive care', 'wellness tips'];
    }

    /**
     * Response for non-health topics
     */
    private function getNonHealthTopicResponse(): array
    {
        return [
            'message' => "I'm designed to help with health and wellness topics only. I can assist with questions about nutrition, fitness, mental health, medical conditions, preventive care, and healthy lifestyle choices. What health-related question can I help you with today?",
            'search_results' => [],
            'sources' => [],
            'topic_classification' => 'non_health',
            'confidence' => 1.0,
            'related_topics' => ['nutrition', 'fitness', 'mental health', 'sleep health'],
            'mode' => 'health_wellness',
            'redirect_suggestion' => true
        ];
    }

    /**
     * Error response
     */
    private function getErrorResponse(): array
    {
        return [
            'message' => 'I apologize, but I cannot provide evidence-based health information at this time. Please try again later or consult a doctor for medical advice.',
            'search_results' => [],
            'sources' => [],
            'topic_classification' => 'error',
            'confidence' => 0.0,
            'related_topics' => [],
            'mode' => 'health_wellness',
            'error' => true
        ];
    }

    /**
     * Get wellness tips for a specific category
     */
    public function getWellnessTips(string $category = 'general'): array
    {
        try {
            $tips = [
                'nutrition' => [
                    'Eat a variety of colorful fruits and vegetables daily',
                    'Stay hydrated with 8-10 glasses of water per day',
                    'Choose whole grains over refined grains',
                    'Include lean proteins in your meals'
                ],
                'fitness' => [
                    'Aim for 150 minutes of moderate exercise per week',
                    'Include strength training 2-3 times per week',
                    'Take regular breaks to move throughout the day',
                    'Find activities you enjoy to stay motivated'
                ],
                'mental_health' => [
                    'Practice mindfulness or meditation daily',
                    'Maintain social connections with friends and family',
                    'Get adequate sleep (7-9 hours for adults)',
                    'Seek professional help when needed'
                ],
                'sleep' => [
                    'Maintain a consistent sleep schedule',
                    'Create a relaxing bedtime routine',
                    'Keep your bedroom cool, dark, and quiet',
                    'Avoid screens 1 hour before bedtime'
                ]
            ];

            return [
                'category' => $category,
                'tips' => $tips[$category] ?? $tips['nutrition'],
                'source' => 'Evidence-based wellness recommendations'
            ];

        } catch (\Exception $e) {
            Log::error('Error getting evidence-based wellness tips', ['error' => $e->getMessage()]);
            return ['error' => 'Unable to retrieve evidence-based wellness tips'];
        }
    }
}
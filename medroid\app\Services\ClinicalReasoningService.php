<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ClinicalReasoningService
{
    private $groqService;
    
    public function __construct(GroqService $groqService)
    {
        $this->groqService = $groqService;
    }
    
    /**
     * Generate transparent clinical reasoning process
     */
    public function generateTransparentReasoning($conversation, $mode = 'consultation')
    {
        try {
            $reasoningPrompt = $this->createReasoningPrompt($conversation, $mode);
            $response = $this->callGroqForReasoning($reasoningPrompt);
            
            return $this->formatReasoningResponse($response, $mode);
            
        } catch (\Exception $e) {
            Log::error('Clinical reasoning generation error', [
                'error' => $e->getMessage(),
                'mode' => $mode
            ]);
            
            return $this->getFallbackReasoning();
        }
    }
    
    private function createReasoningPrompt($conversation, $mode)
    {
        $conversationHistory = $this->formatConversationForReasoning($conversation);
        
        $basePrompt = <<<EOT
<clinical_reasoning_expert>
    <role>Expert clinical reasoning specialist providing systematic, thorough medical analysis equivalent to medical exam-level reasoning</role>
    <purpose>Generate comprehensive clinical reasoning that demonstrates the same depth and quality as medical examination analysis</purpose>

    <conversation_context>
$conversationHistory
    </conversation_context>

    <reasoning_framework>
        <step number="1" name="Clinical Information Extraction and Organization">
            <instruction>Systematically identify and organize ALL clinical information from the conversation</instruction>
            <components>
                <component>Chief complaint and presenting symptoms with detailed characteristics</component>
                <component>Symptom analysis: onset, duration, quality, severity, timing, aggravating/relieving factors</component>
                <component>Associated symptoms and comprehensive review of systems</component>
                <component>Past medical history, surgical history, medications, allergies</component>
                <component>Family history and social history (smoking, alcohol, occupation, travel)</component>
                <component>Demographics and risk factors (age, gender, ethnicity)</component>
            </components>
        </step>

        <step number="2" name="Systematic Symptom Analysis">
            <instruction>Analyze each symptom using rigorous medical reasoning principles</instruction>
            <methodology>
                <element>Break down each symptom individually with anatomical correlation</element>
                <element>Consider pathophysiological mechanisms for each symptom</element>
                <element>Evaluate symptom patterns, relationships, and temporal associations</element>
                <element>Assess symptom severity, progression, and functional impact</element>
                <element>Identify red flag symptoms and concerning features</element>
            </methodology>
        </step>

        <step number="3" name="Pathophysiological Reasoning">
            <instruction>Provide detailed explanation of underlying mechanisms that could explain the clinical presentation</instruction>
            <analysis_framework>
                <element>Anatomical systems and structures involved</element>
                <element>Normal physiological processes and how they may be disrupted</element>
                <element>Pathological mechanisms and disease processes</element>
                <element>Molecular and cellular level explanations where relevant</element>
                <element>Disease progression patterns and natural history</element>
            </analysis_framework>
        </step>

        <step number="4" name="Comprehensive Risk Factor Assessment">
            <instruction>Systematically evaluate ALL risk factors using evidence-based medicine principles</instruction>
            <risk_categories>
                <category name="Demographic">Age, gender, ethnicity, genetic predisposition</category>
                <category name="Lifestyle">Smoking, alcohol, diet, exercise, sleep patterns</category>
                <category name="Medical">Previous conditions, surgeries, medications, immunizations</category>
                <category name="Family">Genetic conditions, familial diseases, hereditary patterns</category>
                <category name="Environmental">Occupational exposures, travel history, geographic factors</category>
                <category name="Psychosocial">Stress, mental health, social determinants</category>
            </risk_categories>
        </step>

        <step number="5" name="Differential Diagnosis Reasoning">
            <instruction>Provide systematic reasoning approach to differential diagnosis WITHOUT listing specific diagnoses</instruction>
            <methodology>
                <approach>Explain the reasoning process for considering different categories of conditions</approach>
                <approach>Discuss epidemiological factors and risk stratification</approach>
                <approach>Explain symptom pattern analysis and clinical correlation</approach>
                <reasoning>Focus on the reasoning process, not specific diagnoses (those will be listed separately)</reasoning>
                <evidence>Use evidence-based medicine principles for reasoning approach</evidence>
            </methodology>
        </step>

        <step number="6" name="Clinical Decision Making">
            <instruction>Demonstrate systematic clinical decision-making process WITHOUT investigation details</instruction>
            <decision_framework>
                <element>Prioritization approach based on probability and severity</element>
                <element>Safety considerations and red flag monitoring</element>
                <element>Clinical reasoning for next steps</element>
                <element>Patient education and counseling considerations</element>
                <note>Do NOT include specific investigation strategy or treatment details (these will be in care plan)</note>
            </decision_framework>
        </step>

        <step number="7" name="Confidence Assessment">
            <instruction>Provide transparent assessment of diagnostic confidence and uncertainty</instruction>
            <confidence_elements>
                <element>Level of diagnostic certainty with specific reasoning</element>
                <element>Areas of uncertainty and why they exist</element>
                <element>Additional information needed to increase confidence</element>
                <element>Risk-benefit analysis considerations</element>
            </confidence_elements>
        </step>
    </reasoning_framework>

    <output_format>
        <structure>
            <section name="Step 1: Clinical Information Summary">Comprehensive organization of all clinical data</section>
            <section name="Step 2: Systematic Symptom Analysis">Detailed analysis of each symptom with pathophysiological correlation</section>
            <section name="Step 3: Pathophysiological Reasoning">Underlying mechanisms and disease processes explanation</section>
            <section name="Step 4: Risk Factor Assessment">Comprehensive evaluation of all risk categories</section>
            <section name="Step 5: Differential Diagnosis Reasoning">Systematic reasoning for each potential diagnosis with evidence</section>
            <section name="Step 6: Clinical Decision Making">Prioritization strategy and management approach</section>
            <section name="Step 7: Confidence Assessment">Diagnostic certainty and uncertainty management</section>
        </structure>

        <quality_standards>
            <standard>Match the depth and thoroughness of medical examination reasoning</standard>
            <standard>Use systematic medical thinking equivalent to USMLE/MRCP level analysis</standard>
            <standard>Provide educational value for both patients and medical professionals</standard>
            <standard>Demonstrate transparent, evidence-based clinical decision-making</standard>
            <standard>Include step-by-step logical progression with detailed explanations</standard>
            <standard>Show the same level of analytical rigor as exam mode responses</standard>
        </quality_standards>
    </output_format>

    <transparency_goals>
        <goal>Patient education and engagement through visible reasoning</goal>
        <goal>Medical professional insight into AI decision-making</goal>
        <goal>Trust building through complete transparency</goal>
        <goal>Demonstration of systematic, evidence-based approach</goal>
        <goal>Educational value equivalent to medical examination analysis</goal>
    </transparency_goals>
</clinical_reasoning_expert>

Generate comprehensive clinical reasoning that demonstrates the same level of systematic medical thinking, depth, and analytical rigor as used in medical examinations. Make every step of your reasoning visible and educational.
EOT;

        if ($mode === 'exam_mode') {
            $basePrompt .= "\n\n**EXAM MODE ADDITION:**\nAlso show how this reasoning applies to medical exam question analysis and high-yield teaching points.";
        }
        
        return $basePrompt;
    }
    
    private function formatConversationForReasoning($conversation)
    {
        if (!$conversation || !isset($conversation->messages)) {
            return "No conversation context available.";
        }
        
        $formatted = "Recent conversation:\n";
        $messages = collect($conversation->messages)->take(-6); // Last 6 messages for context
        
        foreach ($messages as $message) {
            $role = $message['role'] === 'user' ? 'Patient' : 'AI Doctor';
            $content = substr($message['content'], 0, 300) . (strlen($message['content']) > 300 ? '...' : '');
            $formatted .= "$role: $content\n\n";
        }
        
        return $formatted;
    }
    
    private function callGroqForReasoning($prompt)
    {
        $apiKey = config('services.groq.api_key');
        $apiUrl = config('services.groq.api_url');
        $model = config('services.groq.model');
        
        $timeout = config('services.medroid.timeouts.reasoning', 45);

        $response = Http::withHeaders([
            'Authorization' => "Bearer $apiKey",
            'Content-Type' => 'application/json',
        ])->timeout($timeout)
        ->post($apiUrl, [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'system', 
                    'content' => 'You are a clinical reasoning expert creating transparent, educational explanations of medical decision-making processes.'
                ],
                ['role' => 'user', 'content' => $prompt]
            ],
            'temperature' => 0.2,
            'max_completion_tokens' => 1500,
            'top_p' => 1,
            'stream' => false,
        ]);
        
        if ($response->successful()) {
            $result = $response->json();
            return $result['choices'][0]['message']['content'] ?? '';
        }
        
        throw new \Exception('Failed to generate clinical reasoning');
    }
    
    private function formatReasoningResponse($response, $mode)
    {
        return [
            'mode' => $mode,
            'transparent_reasoning' => $response,
            'reasoning_steps' => $this->extractReasoningSteps($response),
            'confidence_indicators' => $this->extractConfidenceIndicators($response),
            'decision_tree' => $this->extractDecisionTree($response),
            'educational_value' => $this->assessEducationalValue($response),
            'timestamp' => now()->toISOString()
        ];
    }
    
    private function extractReasoningSteps($response)
    {
        $steps = [];
        
        // Extract numbered or bulleted reasoning steps
        if (preg_match_all('/(?:^|\n)\s*(?:\d+\.|\*|\-)\s*\*\*(.*?)\*\*\s*[-:]\s*(.*?)(?=\n\s*(?:\d+\.|\*|\-)|$)/s', $response, $matches)) {
            for ($i = 0; $i < count($matches[1]); $i++) {
                $steps[] = [
                    'step_title' => trim($matches[1][$i]),
                    'step_content' => trim($matches[2][$i])
                ];
            }
        }
        
        return $steps;
    }
    
    private function extractConfidenceIndicators($response)
    {
        $indicators = [];
        
        // Look for confidence-related phrases
        $confidencePatterns = [
            '/confident.*?because/i',
            '/certainty.*?level/i',
            '/high.*?confidence/i',
            '/low.*?confidence/i',
            '/uncertain.*?about/i'
        ];
        
        foreach ($confidencePatterns as $pattern) {
            if (preg_match_all($pattern, $response, $matches)) {
                $indicators = array_merge($indicators, $matches[0]);
            }
        }
        
        return array_unique($indicators);
    }
    
    private function extractDecisionTree($response)
    {
        $tree = [];
        
        // Look for decision-making language
        if (preg_match_all('/(?:if|when|because|therefore|given that).*?(?:\.|,|\n)/i', $response, $matches)) {
            $tree = array_slice($matches[0], 0, 5); // Top 5 decision points
        }
        
        return $tree;
    }
    
    private function assessEducationalValue($response)
    {
        $educationalMarkers = [
            'teaching_points' => substr_count(strtolower($response), 'teach'),
            'explanations' => substr_count(strtolower($response), 'because'),
            'systematic_approach' => substr_count(strtolower($response), 'systematic'),
            'clinical_pearls' => substr_count(strtolower($response), 'pearl'),
            'length_score' => min(strlen($response) / 1000, 10) // 0-10 based on length
        ];
        
        $totalScore = array_sum($educationalMarkers);
        
        return [
            'markers' => $educationalMarkers,
            'total_score' => $totalScore,
            'educational_level' => $totalScore > 15 ? 'high' : ($totalScore > 8 ? 'medium' : 'low')
        ];
    }
    
    private function getFallbackReasoning()
    {
        return [
            'mode' => 'fallback',
            'transparent_reasoning' => 'Clinical reasoning analysis temporarily unavailable. The AI Doctor is still providing safe, evidence-based medical guidance through its standard consultation process.',
            'reasoning_steps' => [
                [
                    'step_title' => 'Systematic Assessment',
                    'step_content' => 'Following established medical consultation protocols'
                ],
                [
                    'step_title' => 'Safety First',
                    'step_content' => 'Prioritizing patient safety and appropriate care recommendations'
                ]
            ],
            'confidence_indicators' => ['Standard medical consultation confidence maintained'],
            'decision_tree' => ['Following established clinical decision pathways'],
            'educational_value' => [
                'educational_level' => 'basic',
                'markers' => ['systematic_approach' => 1]
            ],
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * Generate reasoning for specific clinical scenarios
     */
    public function generateScenarioReasoning($scenario, $patientData = [])
    {
        try {
            $scenarioPrompt = $this->createScenarioPrompt($scenario, $patientData);
            $response = $this->callGroqForReasoning($scenarioPrompt);

            return $this->formatReasoningResponse($response, 'scenario_analysis');

        } catch (\Exception $e) {
            Log::error('Scenario reasoning generation error', [
                'error' => $e->getMessage(),
                'scenario' => substr($scenario, 0, 100)
            ]);

            return $this->getFallbackReasoning();
        }
    }

    /**
     * Create educational reasoning breakdown
     */
    public function createEducationalBreakdown($medicalConcept, $complexity = 'intermediate')
    {
        try {
            $educationalPrompt = $this->createEducationalPrompt($medicalConcept, $complexity);
            $response = $this->callGroqForReasoning($educationalPrompt);

            return $this->formatEducationalResponse($response, $complexity);

        } catch (\Exception $e) {
            Log::error('Educational breakdown generation error', [
                'error' => $e->getMessage(),
                'concept' => $medicalConcept
            ]);

            return $this->getFallbackEducational($medicalConcept);
        }
    }

    private function createScenarioPrompt($scenario, $patientData)
    {
        $patientInfo = $this->formatPatientData($patientData);

        return <<<EOT
You are providing TRANSPARENT CLINICAL REASONING for this specific medical scenario.

**Clinical Scenario:**
$scenario

**Patient Information:**
$patientInfo

**Your Task:**
Provide a comprehensive clinical reasoning analysis that demonstrates:

1. **Initial Assessment** - First impressions and immediate concerns
2. **Systematic Evaluation** - How you would approach this case systematically
3. **Differential Reasoning** - Building and refining differential diagnosis
4. **Investigation Strategy** - Logical sequence of tests and evaluations
5. **Risk Assessment** - Identifying and prioritizing risks
6. **Management Planning** - Evidence-based treatment approach

**Educational Focus:**
- Show clinical decision-making process
- Explain reasoning behind each step
- Highlight key teaching points
- Demonstrate systematic medical thinking
- Include safety considerations throughout

Make your reasoning transparent and educational for both patients and medical professionals.
EOT;
    }

    private function createEducationalPrompt($concept, $complexity)
    {
        $complexityInstructions = [
            'basic' => 'Use simple language suitable for patients and medical students',
            'intermediate' => 'Use medical terminology with clear explanations for residents and practitioners',
            'advanced' => 'Use advanced medical concepts for specialists and expert-level understanding'
        ];

        $instruction = $complexityInstructions[$complexity] ?? $complexityInstructions['intermediate'];

        return <<<EOT
Create an EDUCATIONAL CLINICAL REASONING breakdown for this medical concept.

**Medical Concept:** $concept
**Complexity Level:** $complexity
**Target Audience:** $instruction

**Educational Framework:**
1. **Concept Foundation** - Core principles and definitions
2. **Clinical Relevance** - Why this matters in practice
3. **Reasoning Process** - How to think about this concept clinically
4. **Common Pitfalls** - What to avoid and why
5. **Clinical Applications** - Real-world usage and examples
6. **Teaching Points** - Key takeaways and memory aids

**Educational Goals:**
- Build understanding from first principles
- Show practical clinical application
- Provide memorable teaching points
- Include common misconceptions to avoid
- Connect to broader medical knowledge

Make this educational, engaging, and clinically relevant.
EOT;
    }

    private function formatPatientData($patientData)
    {
        if (empty($patientData)) {
            return "No specific patient data provided.";
        }

        $formatted = "";
        foreach ($patientData as $key => $value) {
            $formatted .= "- " . ucfirst(str_replace('_', ' ', $key)) . ": $value\n";
        }

        return $formatted;
    }

    private function formatEducationalResponse($response, $complexity)
    {
        return [
            'mode' => 'educational',
            'complexity_level' => $complexity,
            'educational_content' => $response,
            'learning_objectives' => $this->extractLearningObjectives($response),
            'key_concepts' => $this->extractKeyConcepts($response),
            'clinical_applications' => $this->extractClinicalApplications($response),
            'teaching_points' => $this->extractTeachingPoints($response),
            'educational_value' => $this->assessEducationalValue($response),
            'timestamp' => now()->toISOString()
        ];
    }

    private function extractLearningObjectives($response)
    {
        $objectives = [];

        if (preg_match_all('/(?:objective|goal|learn|understand).*?(?:\.|,|\n)/i', $response, $matches)) {
            $objectives = array_slice($matches[0], 0, 5);
        }

        return $objectives;
    }

    private function extractKeyConcepts($response)
    {
        $concepts = [];

        // Look for bolded or emphasized concepts
        if (preg_match_all('/\*\*(.*?)\*\*/i', $response, $matches)) {
            $concepts = array_unique(array_slice($matches[1], 0, 10));
        }

        return $concepts;
    }

    private function extractClinicalApplications($response)
    {
        $applications = [];

        if (preg_match_all('/(?:clinical|practice|application|used).*?(?:\.|,|\n)/i', $response, $matches)) {
            $applications = array_slice($matches[0], 0, 5);
        }

        return $applications;
    }

    private function extractTeachingPoints($response)
    {
        $points = [];

        if (preg_match_all('/(?:remember|key point|important|pearl).*?(?:\.|,|\n)/i', $response, $matches)) {
            $points = array_slice($matches[0], 0, 5);
        }

        return $points;
    }

    private function getFallbackEducational($concept)
    {
        return [
            'mode' => 'educational_fallback',
            'complexity_level' => 'basic',
            'educational_content' => "Educational breakdown for '$concept' is temporarily unavailable. Please consult medical textbooks or speak with a healthcare provider for detailed information about this topic.",
            'learning_objectives' => ["Understand the basic concept of $concept"],
            'key_concepts' => [$concept],
            'clinical_applications' => ["Consult healthcare provider for clinical guidance"],
            'teaching_points' => ["Seek professional medical education resources"],
            'educational_value' => [
                'educational_level' => 'basic',
                'markers' => ['fallback' => 1]
            ],
            'timestamp' => now()->toISOString()
        ];
    }
}

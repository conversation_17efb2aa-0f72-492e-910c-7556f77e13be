<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class MedroidModeDetector
{
    /**
     * Detect the appropriate mode for Medroid based on user input
     * Returns: 'exam_mode' or 'consultation'
     */
    public function detectMode($message, $conversationContext = [])
    {
        try {
            // 1. Check for exam mode triggers
            $examModeResult = $this->detectExamMode($message);
            if ($examModeResult['mode'] === 'exam_mode') {
                return $examModeResult;
            }

            // 2. Everything else goes to consultation mode (emergency detection handled within consultation)
            return ['mode' => 'consultation'];
        } catch (\Exception $e) {
            Log::error('Error in mode detection', [
                'message' => $e->getMessage(),
                'user_message' => $message
            ]);

            // Default to consultation mode for safety
            return ['mode' => 'consultation'];
        }
    }
    
    /**
     * Detect exam mode triggers
     */
    private function detectExamMode($message)
    {
        // Check for simple exam mode trigger
        if (preg_match('/#Exam Mode#/i', $message)) {
            return [
                'mode' => 'exam_mode',
                'exam_type' => $this->parseExamType($message)
            ];
        }

        return ['mode' => 'unknown'];
    }

    /**
     * Parse specific exam type from message content
     */
    private function parseExamType($message)
    {
        // Look for exam type indicators in the message content
        if (preg_match('/USMLE.*Step\s*1/i', $message)) return 'usmle_step1';
        if (preg_match('/USMLE.*Step\s*2.*CK/i', $message)) return 'usmle_step2_ck';
        if (preg_match('/USMLE.*Step\s*2.*CS/i', $message)) return 'usmle_step2_cs';
        if (preg_match('/MRCP.*Part\s*1/i', $message)) return 'mrcp_part1';
        if (preg_match('/MRCP.*Part\s*2/i', $message)) return 'mrcp_part2';
        if (preg_match('/MRCP.*PACES/i', $message)) return 'mrcp_paces';
        if (preg_match('/USMLE/i', $message)) return 'usmle_general';
        if (preg_match('/MRCP/i', $message)) return 'mrcp_general';

        // Default to general exam mode if no specific type detected
        return 'general_exam';
    }
    

    

    
    /**
     * Get mode-specific instructions for the AI
     */
    public function getModeInstructions($modeResult)
    {
        switch ($modeResult['mode']) {
            case 'exam_mode':
                return $this->getExamModeInstructions($modeResult['exam_type'] ?? 'unknown');
            case 'consultation':
            default:
                return 'CONSULTATION MODE: Follow standard medical consultation protocol with emergency detection.';
        }
    }
    
    /**
     * Get exam-specific instructions
     */
    private function getExamModeInstructions($examType)
    {
        $baseInstruction = 'EXAM MODE ACTIVATED: Focus on systematic clinical reasoning for medical examination success. ';

        switch ($examType) {
            case 'usmle_step1':
                return $baseInstruction . 'Emphasize basic science mechanisms and pathophysiology.';
            case 'usmle_step2_ck':
                return $baseInstruction . 'Focus on clinical decision-making and patient management.';
            case 'usmle_general':
                return $baseInstruction . 'Apply USMLE-style systematic reasoning and evidence-based medicine.';
            case 'mrcp_part1':
            case 'mrcp_part2':
                return $baseInstruction . 'Apply UK clinical guidelines and NICE recommendations.';
            case 'mrcp_general':
                return $baseInstruction . 'Apply UK clinical guidelines and Royal College standards.';
            case 'general_exam':
            default:
                return $baseInstruction . 'Use systematic medical reasoning approach for exam success.';
        }
    }
}

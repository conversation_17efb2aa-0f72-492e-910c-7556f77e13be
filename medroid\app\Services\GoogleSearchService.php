<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GoogleSearchService
{
    private $apiEndpoint;
    private $projectId;
    
    public function __construct()
    {
        $projectId = config('services.google_cloud.project_id', '1026523769008');
        $searchEngineId = config('services.google_cloud.search_engine_id', 'trusted-sources_1748608202932');

        $this->apiEndpoint = "https://discoveryengine.googleapis.com/v1alpha/projects/{$projectId}/locations/global/collections/default_collection/engines/{$searchEngineId}/servingConfigs/default_search:search";
        $this->projectId = $projectId;
    }
    
    /**
     * Search for evidence-based medical information
     */
    public function searchMedicalEvidence($query, $options = [])
    {
        try {
            $optimizedQuery = $this->optimizeSearchQuery($query);
            
            $searchPayload = [
                'query' => $optimizedQuery,
                'pageSize' => $options['pageSize'] ?? 10,
                'queryExpansionSpec' => ['condition' => 'AUTO'],
                'spellCorrectionSpec' => ['mode' => 'AUTO'],
                'languageCode' => 'en-US',
                'userInfo' => ['timeZone' => 'Europe/London']
            ];
            
            $token = $this->getAccessToken();
            
            if (!$token) {
                Log::warning('Google Cloud access token not available');
                return ['error' => 'Search service configuration issue'];
            }
            
            $timeout = config('services.medroid.timeouts.search', 30);

            $response = Http::withHeaders([
                'Authorization' => "Bearer $token",
                'Content-Type' => 'application/json',
            ])->timeout($timeout)
            ->post($this->apiEndpoint, $searchPayload);
            
            if ($response->successful()) {
                return $this->processSearchResults($response->json());
            }
            
            Log::error('Google Search API error', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            
            return ['error' => 'Search service unavailable'];
            
        } catch (\Exception $e) {
            Log::error('Google Search error', [
                'message' => $e->getMessage(),
                'query' => $query
            ]);
            
            return ['error' => 'Search service error'];
        }
    }
    
    /**
     * Optimize search query for medical evidence
     */
    private function optimizeSearchQuery($query)
    {
        // Add medical authority terms for better results
        $authorityTerms = ['evidence-based', 'clinical guidelines', 'medical recommendations'];
        $randomAuthority = $authorityTerms[array_rand($authorityTerms)];
        
        // Add safety terms for wellness queries
        $safetyTerms = ['safety', 'contraindications', 'precautions'];
        $randomSafety = $safetyTerms[array_rand($safetyTerms)];
        
        return "$query $randomAuthority $randomSafety";
    }
    
    /**
     * Get Google Cloud access token
     */
    private function getAccessToken()
    {
        try {
            // Try to get Google Cloud access token
            $output = shell_exec('gcloud auth print-access-token 2>/dev/null');
            return $output ? trim($output) : null;
        } catch (\Exception $e) {
            Log::error('Failed to get Google Cloud access token', [
                'message' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Process search results
     */
    private function processSearchResults($results)
    {
        $processed = [
            'sources' => [],
            'summary' => '',
            'evidence_quality' => 'unknown'
        ];
        
        if (isset($results['results'])) {
            foreach ($results['results'] as $result) {
                $processed['sources'][] = [
                    'title' => $result['document']['derivedStructData']['title'] ?? 'Unknown',
                    'url' => $result['document']['derivedStructData']['link'] ?? '',
                    'snippet' => $result['document']['derivedStructData']['snippet'] ?? '',
                    'authority' => $this->determineAuthority($result['document']['derivedStructData']['link'] ?? '')
                ];
            }
        }
        
        return $processed;
    }
    
    /**
     * Determine authority level of source
     */
    private function determineAuthority($url)
    {
        $tier1Authorities = [
            'cdc.gov', 'nih.gov', 'nice.org.uk', 'mayoclinic.org', 'uptodate.com',
            'who.int', 'fda.gov', 'nhs.uk', 'bmj.com', 'nejm.org'
        ];
        
        $tier2Authorities = [
            'medscape.com', 'hopkinsmedicine.org', 'harvard.edu', 'webmd.com',
            'clevelandclinic.org', 'mountsinai.org', 'stanfordhealthcare.org'
        ];
        
        foreach ($tier1Authorities as $authority) {
            if (strpos($url, $authority) !== false) return 'tier1';
        }
        
        foreach ($tier2Authorities as $authority) {
            if (strpos($url, $authority) !== false) return 'tier2';
        }
        
        return 'tier3';
    }
    
    /**
     * Search for specific medical topic with safety focus
     */
    public function searchWithSafetyFocus($topic, $userContext = [])
    {
        $safetyQuery = "$topic safety precautions contraindications medical guidelines";
        
        // Add user-specific context if available
        if (!empty($userContext['age'])) {
            $safetyQuery .= " age " . $userContext['age'];
        }
        
        if (!empty($userContext['gender'])) {
            $safetyQuery .= " " . $userContext['gender'];
        }
        
        return $this->searchMedicalEvidence($safetyQuery);
    }
    
    /**
     * Check if search service is available
     */
    public function isAvailable()
    {
        try {
            $token = $this->getAccessToken();
            return !empty($token);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get fallback recommendations when search is unavailable
     */
    public function getFallbackRecommendations($query)
    {
        return [
            'sources' => [
                [
                    'title' => 'Consult Healthcare Provider',
                    'url' => '',
                    'snippet' => 'For accurate medical information, please consult with a qualified healthcare provider.',
                    'authority' => 'general'
                ]
            ],
            'summary' => 'Search service temporarily unavailable. Please consult healthcare provider for specific medical guidance.',
            'evidence_quality' => 'unavailable'
        ];
    }

    /**
     * Format sources for citation display with inline references
     */
    public function formatSourcesCitation($sources)
    {
        if (empty($sources)) {
            return '';
        }

        $citation = "\n\n<details>\n<summary><strong>📚 Sources</strong> (Click to expand)</summary>\n\n";

        foreach ($sources as $index => $source) {
            $sourceNumber = $index + 1;
            $authorityIcon = $this->getAuthorityIcon($source['authority']);
            $citation .= "{$sourceNumber}. {$authorityIcon} **{$source['title']}**  \n";
            $citation .= "   " . parse_url($source['url'], PHP_URL_HOST) . " - [{$source['url']}]({$source['url']})  \n\n";
        }

        $citation .= "</details>";

        return $citation;
    }

    /**
     * Get authority level icon
     */
    private function getAuthorityIcon($authority)
    {
        switch ($authority) {
            case 'tier1':
                return '🏥'; // High authority medical sources
            case 'tier2':
                return '🩺'; // Medium authority medical sources
            default:
                return '📄'; // General sources
        }
    }

    /**
     * Generate search-enhanced response with proper citations
     */
    public function enhanceResponseWithSearch($query, $baseResponse)
    {
        $searchResults = $this->searchMedicalEvidence($query);

        if (!isset($searchResults['error']) && !empty($searchResults['sources'])) {
            // Add source citations at the end
            $sourceCitation = $this->formatSourcesCitation($searchResults['sources']);
            $enhancedResponse = $baseResponse . $sourceCitation;

            return [
                'response' => $enhancedResponse,
                'search_results' => $searchResults,
                'has_citations' => true,
                'source_count' => count($searchResults['sources'])
            ];
        }

        return [
            'response' => $baseResponse,
            'search_results' => $searchResults,
            'has_citations' => false,
            'source_count' => 0
        ];
    }
}
